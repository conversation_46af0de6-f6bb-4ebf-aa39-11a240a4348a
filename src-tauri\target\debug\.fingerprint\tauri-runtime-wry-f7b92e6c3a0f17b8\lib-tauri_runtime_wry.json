{"rustc": 1842507548689473721, "features": "[\"objc-exception\"]", "declared_features": "[\"arboard\", \"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"linux-headers\", \"macos-private-api\", \"objc-exception\", \"system-tray\", \"tracing\"]", "target": 1901661049345253480, "profile": 2241668132362809309, "path": 15012677536052896636, "deps": [[4381063397040571828, "webview2_com", false, 9790401245228756296], [7653476968652377684, "windows", false, 6481264962600619771], [8292277814562636972, "tauri_utils", false, 2784821817036372612], [8319709847752024821, "uuid", false, 1852090744233057605], [8391357152270261188, "wry", false, 9717958474749287424], [11693073011723388840, "raw_window_handle", false, 17418342113172323224], [13208667028893622512, "rand", false, 7451151832161222921], [14162324460024849578, "tauri_runtime", false, 17120820587643497685], [16228250612241359704, "build_script_build", false, 16196867840505060753]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-f7b92e6c3a0f17b8\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}