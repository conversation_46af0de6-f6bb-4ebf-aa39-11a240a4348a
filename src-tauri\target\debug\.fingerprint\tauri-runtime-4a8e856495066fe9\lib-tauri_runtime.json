{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 10306386172444932100, "profile": 7668817577240348489, "path": 17822628632177178835, "deps": [[3150220818285335163, "url", false, 2359489799148251420], [4381063397040571828, "webview2_com", false, 9790401245228756296], [4405182208873388884, "http", false, 2295860433350784480], [7653476968652377684, "windows", false, 6481264962600619771], [8008191657135824715, "thiserror", false, 8814677142609614397], [8292277814562636972, "tauri_utils", false, 2784821817036372612], [8319709847752024821, "uuid", false, 1852090744233057605], [8866577183823226611, "http_range", false, 2550995987400015056], [9689903380558560274, "serde", false, 7244005325934044490], [11693073011723388840, "raw_window_handle", false, 17418342113172323224], [13208667028893622512, "rand", false, 7451151832161222921], [14162324460024849578, "build_script_build", false, 2132309613700367837], [16362055519698394275, "serde_json", false, 2907422950230677828]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-4a8e856495066fe9\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}