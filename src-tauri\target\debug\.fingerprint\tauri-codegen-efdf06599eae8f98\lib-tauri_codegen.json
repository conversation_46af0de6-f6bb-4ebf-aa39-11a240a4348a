{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"regex\", \"shell-scope\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\", \"shell-scope\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 12569435529277108869, "deps": [[3060637413840920116, "proc_macro2", false, 5101529325097360867], [4899080583175475170, "semver", false, 15308277562848160592], [7392050791754369441, "ico", false, 14331904798462588578], [8008191657135824715, "thiserror", false, 3589571385162951327], [8292277814562636972, "tauri_utils", false, 8770579856022687252], [8319709847752024821, "uuid", false, 5953131339457476852], [9451456094439810778, "regex", false, 295104199578293755], [9689903380558560274, "serde", false, 7599971217213715363], [9857275760291862238, "sha2", false, 7904460164224431746], [10301936376833819828, "json_patch", false, 15790135984951922044], [12687914511023397207, "png", false, 14788650973187613366], [14132538657330703225, "brotli", false, 5848854700438628636], [15622660310229662834, "walkdir", false, 12526023867962596156], [16362055519698394275, "serde_json", false, 11629507655132901778], [17990358020177143287, "quote", false, 17513530084134125339], [18066890886671768183, "base64", false, 14540169921128798691]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-efdf06599eae8f98\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}