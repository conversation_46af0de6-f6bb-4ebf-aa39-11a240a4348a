{"rustc": 1842507548689473721, "features": "[\"compression\", \"custom-protocol\", \"default\", \"dialog\", \"dialog-ask\", \"dialog-confirm\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"path-all\", \"process-exit\", \"process-relaunch\", \"regex\", \"rfd\", \"shell-open\", \"shell-open-api\", \"sys-locale\", \"tauri-runtime-wry\", \"wry\"]", "declared_features": "[\"api-all\", \"app-all\", \"app-hide\", \"app-show\", \"base64\", \"bytes\", \"clap\", \"cli\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-confirm\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"dox\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-extract-api\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-multipart\", \"http-request\", \"ico\", \"icon-ico\", \"icon-png\", \"indexmap\", \"infer\", \"isolation\", \"linux-protocol-headers\", \"macos-private-api\", \"minisign-verify\", \"native-tls-vendored\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"os_pipe\", \"path-all\", \"png\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-all\", \"protocol-asset\", \"regex\", \"reqwest\", \"reqwest-client\", \"reqwest-native-tls-vendored\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"sys-locale\", \"system-tray\", \"tauri-runtime-wry\", \"test\", \"time\", \"tracing\", \"updater\", \"win7-notifications\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-data-url\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-closable\", \"window-set-content-protected\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-ignore-cursor-events\", \"window-set-max-size\", \"window-set-maximizable\", \"window-set-min-size\", \"window-set-minimizable\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"windows7-compat\", \"wry\", \"zip\"]", "target": 12223948975794516716, "profile": 7668817577240348489, "path": 14922135748753573251, "deps": [[40386456601120721, "percent_encoding", false, 15399487207053890585], [947818755262499932, "notify_rust", false, 2217995629614892943], [1260461579271933187, "serialize_to_javascript", false, 843100731591357222], [1441306149310335789, "tempfile", false, 17868071258332791275], [3150220818285335163, "url", false, 2359489799148251420], [3722963349756955755, "once_cell", false, 17868233020703470270], [3988549704697787137, "open", false, 7662817631276277032], [4381063397040571828, "webview2_com", false, 9790401245228756296], [4405182208873388884, "http", false, 2295860433350784480], [4450062412064442726, "dirs_next", false, 1465993867069742485], [4899080583175475170, "semver", false, 18206700911497093579], [5024769281214949041, "os_info", false, 11152647300776644130], [5099504066399492044, "rfd", false, 6054309963721217234], [5180608563399064494, "tauri_macros", false, 4555054998900079036], [5610773616282026064, "build_script_build", false, 2450075018954435795], [5986029879202738730, "log", false, 391949849809365867], [7653476968652377684, "windows", false, 6481264962600619771], [8008191657135824715, "thiserror", false, 8814677142609614397], [8292277814562636972, "tauri_utils", false, 2784821817036372612], [8319709847752024821, "uuid", false, 1852090744233057605], [9451456094439810778, "regex", false, 4428533771886550896], [9623796893764309825, "ignore", false, 14879106460482139013], [9689903380558560274, "serde", false, 7244005325934044490], [9920160576179037441, "getrandom", false, 13936940179806012516], [10629569228670356391, "futures_util", false, 9544930713638697627], [11601763207901161556, "tar", false, 1752872475557172735], [11693073011723388840, "raw_window_handle", false, 17418342113172323224], [11989259058781683633, "dunce", false, 13008008023573122482], [12986574360607194341, "serde_repr", false, 8672939529100098032], [13208667028893622512, "rand", false, 7451151832161222921], [13625485746686963219, "anyhow", false, 10106888276199890854], [14162324460024849578, "tauri_runtime", false, 17120820587643497685], [14564311161534545801, "encoding_rs", false, 1232932660917386274], [14618885535728128396, "sys_locale", false, 16796099407795769214], [16228250612241359704, "tauri_runtime_wry", false, 2394561065920567116], [16362055519698394275, "serde_json", false, 2907422950230677828], [17155886227862585100, "glob", false, 10468494251249478677], [17278893514130263345, "state", false, 1407150688665698058], [17531218394775549125, "tokio", false, 1798005279649335019], [17772299992546037086, "flate2", false, 6317955283595342357]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-5fee83854084abc2\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}