{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"codegen\", \"config-json5\", \"config-toml\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 16335202704479430798, "path": 2117614603859027765, "deps": [[4450062412064442726, "dirs_next", false, 17940712193849031172], [4899080583175475170, "semver", false, 15308277562848160592], [7468248713591957673, "cargo_toml", false, 964750371519206674], [8292277814562636972, "tauri_utils", false, 8770579856022687252], [9689903380558560274, "serde", false, 7599971217213715363], [10301936376833819828, "json_patch", false, 15790135984951922044], [13077543566650298139, "heck", false, 9134817796626205958], [13625485746686963219, "anyhow", false, 14817484948841222942], [14189313126492979171, "tauri_winres", false, 2950245881750097338], [15622660310229662834, "walkdir", false, 12526023867962596156], [16362055519698394275, "serde_json", false, 11629507655132901778]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-ccf431e560320f2a\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}