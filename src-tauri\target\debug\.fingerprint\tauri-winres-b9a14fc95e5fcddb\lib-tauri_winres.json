{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 10555948925697328552, "profile": 2225463790103693989, "path": 13655735013392903093, "deps": [[1293861355733423614, "toml", false, 17395705094515821512], [11501392865286155686, "embed_resource", false, 5284657097524663964]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winres-b9a14fc95e5fcddb\\dep-lib-tauri_winres", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}