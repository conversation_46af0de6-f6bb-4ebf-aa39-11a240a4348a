{"rustc": 1842507548689473721, "features": "[\"compression\", \"custom-protocol\", \"shell-scope\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"shell-scope\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 10727301385320622173, "deps": [[2713742371683562785, "syn", false, 2811436701265808131], [3060637413840920116, "proc_macro2", false, 5101529325097360867], [8292277814562636972, "tauri_utils", false, 8770579856022687252], [13077543566650298139, "heck", false, 9134817796626205958], [17492769205600034078, "tauri_codegen", false, 8807175365368086988], [17990358020177143287, "quote", false, 17513530084134125339]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-577533b241f64db6\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}