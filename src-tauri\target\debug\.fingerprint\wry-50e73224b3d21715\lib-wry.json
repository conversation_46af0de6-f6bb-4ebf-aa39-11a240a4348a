{"rustc": 1842507548689473721, "features": "[\"file-drop\", \"objc-exception\", \"protocol\"]", "declared_features": "[\"default\", \"devtools\", \"dox\", \"file-drop\", \"fullscreen\", \"linux-headers\", \"objc-exception\", \"protocol\", \"tracing\", \"transparent\", \"tray\"]", "target": 2463569863749872413, "profile": 2241668132362809309, "path": 5119509926418685058, "deps": [[3007252114546291461, "tao", false, 12097223215261977150], [3150220818285335163, "url", false, 2359489799148251420], [3540822385484940109, "windows_implement", false, 6851144306166796328], [3722963349756955755, "once_cell", false, 17868233020703470270], [4381063397040571828, "webview2_com", false, 9790401245228756296], [4405182208873388884, "http", false, 2295860433350784480], [4684437522915235464, "libc", false, 127354914144300171], [5986029879202738730, "log", false, 391949849809365867], [7653476968652377684, "windows", false, 6481264962600619771], [8008191657135824715, "thiserror", false, 8814677142609614397], [8391357152270261188, "build_script_build", false, 434047854387824048], [9689903380558560274, "serde", false, 7244005325934044490], [11989259058781683633, "dunce", false, 13008008023573122482], [16362055519698394275, "serde_json", false, 2907422950230677828]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wry-50e73224b3d21715\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}